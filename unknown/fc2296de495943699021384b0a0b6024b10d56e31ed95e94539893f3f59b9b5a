const { ethers, network } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("🌉 开始部署BSC端TokenBridge合约");
    console.log("================================================");
    console.log("网络:", network.name);
    console.log("链ID:", network.config.chainId);
    console.log("时间:", new Date().toISOString());
    
    const [deployer, treasury, operator] = await ethers.getSigners();
    console.log("部署账户:", deployer.address);
    console.log("国库账户:", treasury.address);
    console.log("操作员账户:", operator.address);
    
    // 获取已部署的代币合约地址
    let pxtokenAddress, patokenAddress;
    
    try {
        const coreFile = path.join(__dirname, "../../deployments", network.name, "core-deployment.json");
        const coreDeployment = JSON.parse(fs.readFileSync(coreFile, 'utf8'));
        
        pxtokenAddress = coreDeployment.contracts.PXToken.address;
        patokenAddress = coreDeployment.contracts.PAToken.address;
        
        console.log("✅ 已加载核心代币地址");
        console.log("PXT代币地址:", pxtokenAddress);
        console.log("PAT代币地址:", patokenAddress);
    } catch (error) {
        console.error("❌ 未找到核心代币部署文件，请先运行:");
        console.error("npx hardhat run scripts/deploy/01-deploy-core-tokens.js --network", network.name);
        process.exit(1);
    }
    
    console.log("\n=== 1. 部署TokenBridge合约 ===");

    const TokenBridge = await ethers.getContractFactory("TokenBridge");
    const tokenBridge = await TokenBridge.deploy(
        pxtokenAddress,  // PXT代币地址
        patokenAddress,  // PAT代币地址
        treasury.address // 费用接收地址（使用国库地址）
    );
    await tokenBridge.deployed();
    
    console.log("✅ TokenBridge部署成功:", tokenBridge.address);
    
    console.log("\n=== 2. 设置管理员权限 ===");

    // 设置部署者为管理员
    await tokenBridge.setAdministrator(deployer.address, true);
    console.log("✅ 已设置部署者为管理员");

    console.log("\n=== 3. 配置PXPAC链支持 ===");

    // PXPAC链配置
    const PXPAC_CHAIN_ID = 11;
    const PXPAC_CHAIN_NAME = "PXPAC";

    // 设置链名称
    await tokenBridge.setChainName(PXPAC_CHAIN_ID, PXPAC_CHAIN_NAME);
    console.log(`✅ 已设置PXPAC链名称: ${PXPAC_CHAIN_NAME}`);

    // 验证者配置
    const validators = [deployer.address, treasury.address, operator.address];
    const requiredConfirmations = 2; // 2/3多数

    // 费用配置
    const feeConfig = {
        baseFee: ethers.utils.parseEther("0.001"),      // 基础费用 0.001 BNB
        percentFee: 50,                                  // 0.5% 百分比费用 (50/10000)
        minFee: ethers.utils.parseEther("0.0005"),      // 最小费用 0.0005 BNB
        maxFee: ethers.utils.parseEther("0.1")          // 最大费用 0.1 BNB
    };

    // 一次性添加PXPAC链支持（包含验证者和费用配置）
    console.log("正在添加PXPAC链支持...");
    const addChainTx = await tokenBridge.addSupportedChain(
        PXPAC_CHAIN_ID,
        validators,
        requiredConfirmations,
        feeConfig.baseFee,
        feeConfig.percentFee,
        feeConfig.minFee,
        feeConfig.maxFee
    );

    console.log("等待交易确认...");
    const receipt = await addChainTx.wait();
    console.log("交易确认成功，区块:", receipt.blockNumber);

    console.log("✅ 已添加PXPAC链支持");
    console.log("  链ID:", PXPAC_CHAIN_ID);
    console.log("  验证者数量:", validators.length);
    console.log("  需要确认数:", requiredConfirmations);
    console.log("  基础费用:", ethers.utils.formatEther(feeConfig.baseFee), "BNB");
    console.log("  百分比费用:", feeConfig.percentFee / 100, "%");
    console.log("  最小费用:", ethers.utils.formatEther(feeConfig.minFee), "BNB");
    console.log("  最大费用:", ethers.utils.formatEther(feeConfig.maxFee), "BNB");
    
    console.log("\n=== 4. 验证配置 ===");

    // 验证链支持
    const isPxpacSupported = await tokenBridge.supportedChainIds(PXPAC_CHAIN_ID);
    console.log("PXPAC链支持状态:", isPxpacSupported ? "✅ 支持" : "❌ 不支持");

    // 验证验证者
    const chainValidators = await tokenBridge.getChainValidators(PXPAC_CHAIN_ID);
    console.log("PXPAC链验证者数量:", chainValidators.length);
    console.log("验证者地址:");
    for (let i = 0; i < chainValidators.length; i++) {
        console.log(`  ${i + 1}. ${chainValidators[i]}`);
    }

    // 验证费用配置
    const chainFee = await tokenBridge.chainFees(PXPAC_CHAIN_ID);
    console.log("PXPAC链费用配置:");
    console.log("  基础费用:", ethers.utils.formatEther(chainFee.baseFee), "BNB");
    console.log("  百分比费用:", chainFee.percentFee.toString() / 100, "%");
    console.log("  最小费用:", ethers.utils.formatEther(chainFee.minFee), "BNB");
    console.log("  最大费用:", ethers.utils.formatEther(chainFee.maxFee), "BNB");
    
    // 保存部署信息
    const deploymentInfo = {
        network: network.name,
        chainId: network.config.chainId,
        timestamp: new Date().toISOString(),
        deployer: deployer.address,
        contracts: {
            TokenBridge: {
                address: tokenBridge.address,
                description: "BSC端跨链桥合约"
            }
        },
        configuration: {
            supportedChains: [
                {
                    chainId: PXPAC_CHAIN_ID,
                    name: PXPAC_CHAIN_NAME,
                    validators: validators,
                    requiredConfirmations: requiredConfirmations
                }
            ],
            feeConfig: {
                baseFee: ethers.utils.formatEther(feeConfig.baseFee) + " BNB",
                percentFee: (feeConfig.percentFee / 100) + "%",
                minFee: ethers.utils.formatEther(feeConfig.minFee) + " BNB",
                maxFee: ethers.utils.formatEther(feeConfig.maxFee) + " BNB"
            }
        },
        dependencies: {
            PXToken: pxtokenAddress,
            PAToken: patokenAddress
        }
    };
    
    // 保存部署文件
    const deploymentDir = path.join(__dirname, "../../deployments", network.name);
    const deploymentFile = path.join(deploymentDir, "bridge-deployment.json");
    fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
    
    console.log("\n=== 部署完成 ===");
    console.log("部署信息已保存到:", deploymentFile);
    console.log("\n📋 重要信息:");
    console.log("TokenBridge地址:", tokenBridge.address);
    console.log("支持的目标链: PXPAC (链ID: 11)");
    console.log("验证者数量:", validators.length);
    console.log("需要确认数:", requiredConfirmations);
    console.log("费用接收地址:", treasury.address);

    console.log("\n🔧 下一步:");
    console.log("1. 配置跨链桥连接: npx hardhat run scripts/bridge/setup-bridge-connection.js --network", network.name);
    console.log("2. 测试跨链功能: npx hardhat run scripts/test/05-bridge-test.js --network", network.name);

    return {
        tokenBridge: tokenBridge.address,
        supportedChains: [PXPAC_CHAIN_ID],
        validators: validators,
        requiredConfirmations: requiredConfirmations,
        feeReceiver: treasury.address
    };
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("TokenBridge部署失败:", error);
            process.exit(1);
        });
}

module.exports = main;
