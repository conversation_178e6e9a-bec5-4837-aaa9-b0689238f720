// 检查PXA链上的wPAT余额
const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("\n🔍 检查PXA链上的wPAT余额");
    console.log("================================");
    
    const [deployer] = await ethers.getSigners();
    console.log("🌐 当前网络:", network.name);
    console.log("📝 检查地址:", deployer.address);
    console.log("⏰ 时间:", new Date().toISOString());
    
    // 从部署记录加载合约地址
    let bridgeReceiverAddress, wPATAddress;

    try {
        // 加载完整部署记录（包含BridgeReceiver）
        const completeDeploymentFiles = fs.readdirSync(path.join(__dirname, "../../deployments"))
            .filter(file => file.startsWith("complete-deployment-") && file.endsWith(".json"));

        if (completeDeploymentFiles.length > 0) {
            const completeFile = path.join(__dirname, "../../deployments", completeDeploymentFiles[0]);
            const completeDeployment = JSON.parse(fs.readFileSync(completeFile, 'utf8'));
            bridgeReceiverAddress = completeDeployment.contracts.bridgeReceiver;
            console.log("✅ 已加载完整部署信息");
        }

        // 加载wPAT部署记录
        const wpatDeploymentFiles = fs.readdirSync(path.join(__dirname, "../../deployments"))
            .filter(file => file.startsWith("wpat-deployment-") && file.endsWith(".json"));

        if (wpatDeploymentFiles.length > 0) {
            const wpatFile = path.join(__dirname, "../../deployments", wpatDeploymentFiles[0]);
            const wpatDeployment = JSON.parse(fs.readFileSync(wpatFile, 'utf8'));
            wPATAddress = wpatDeployment.contracts.wPAT.address;
            console.log("✅ 已加载wPAT部署信息");
        }

    } catch (error) {
        console.error("❌ 加载部署文件失败:", error.message);
        console.log("💡 请确保已部署PXA链的桥接合约和wPAT代币");
        process.exit(1);
    }
    
    console.log("📝 合约地址:");
    console.log("- BridgeReceiver:", bridgeReceiverAddress);
    console.log("- wPAT代币:", wPATAddress || "未部署");
    
    if (!wPATAddress) {
        console.error("❌ wPAT代币合约未部署");
        console.log("💡 请先部署wPAT代币合约");
        process.exit(1);
    }
    
    // 连接wPAT合约
    const wPAT = await ethers.getContractAt("wPAT", wPATAddress);
    const bridgeReceiver = await ethers.getContractAt("BridgeReceiver", bridgeReceiverAddress);
    
    console.log("\n💰 检查余额:");
    
    try {
        // 检查wPAT余额
        const wPATBalance = await wPAT.balanceOf(deployer.address);
        console.log("- wPAT余额:", ethers.formatEther(wPATBalance), "wPAT");
        
        // 检查wPAT总供应量
        const totalSupply = await wPAT.totalSupply();
        console.log("- wPAT总供应量:", ethers.formatEther(totalSupply), "wPAT");
        
        // 检查PXA余额
        const pxaBalance = await ethers.provider.getBalance(deployer.address);
        console.log("- PXA余额:", ethers.formatEther(pxaBalance), "PXA");
        
        console.log("\n📊 跨链状态分析:");
        
        if (wPATBalance > 0) {
            console.log("✅ 跨链成功！已收到", ethers.formatEther(wPATBalance), "wPAT");
            
            // 检查是否是预期的10,000 wPAT
            const expectedAmount = ethers.parseEther("10000");
            if (wPATBalance >= expectedAmount) {
                console.log("✅ 收到的wPAT数量符合预期（≥10,000 wPAT）");
            } else {
                console.log("⚠️ 收到的wPAT数量少于预期（应为10,000 wPAT）");
            }
        } else {
            console.log("❌ 未收到wPAT代币");
            console.log("💡 可能原因:");
            console.log("   1. PXA链验证者尚未处理跨链请求");
            console.log("   2. 跨链请求处理失败");
            console.log("   3. wPAT合约地址不正确");
        }
        
        console.log("\n🔍 检查桥接合约状态:");
        
        // 检查桥接合约的一些状态
        try {
            const bridgeBalance = await ethers.provider.getBalance(bridgeReceiverAddress);
            console.log("- 桥接合约PXA余额:", ethers.formatEther(bridgeBalance), "PXA");
            
            // 如果有其他状态查询方法，可以在这里添加
            console.log("✅ 桥接合约状态正常");
            
        } catch (error) {
            console.log("⚠️ 无法查询桥接合约状态:", error.message);
        }
        
        console.log("\n📋 总结:");
        console.log("================================");
        if (wPATBalance > 0) {
            console.log("🎉 跨链验证成功！");
            console.log("✅ BSC → PXA 跨链已完成");
            console.log("✅ 收到wPAT:", ethers.formatEther(wPATBalance));
            console.log("✅ 双链系统运行正常");
            
            console.log("\n🚀 下一步建议:");
            console.log("1. 测试wPAT代币的转账功能");
            console.log("2. 测试反向跨链（PXA → BSC）");
            console.log("3. 测试其他DApp功能");
            console.log("4. 准备部署到测试网");
        } else {
            console.log("⏳ 跨链处理中或失败");
            console.log("💡 建议:");
            console.log("1. 等待几分钟后重新检查");
            console.log("2. 检查PXA链验证者日志");
            console.log("3. 验证BSC跨链交易状态");
        }
        
        return {
            success: wPATBalance > 0,
            wPATBalance: ethers.formatEther(wPATBalance),
            totalSupply: ethers.formatEther(totalSupply),
            pxaBalance: ethers.formatEther(pxaBalance)
        };
        
    } catch (error) {
        console.error("❌ 检查余额失败:", error.message);
        
        if (error.message.includes("call revert exception")) {
            console.log("💡 可能原因: wPAT合约地址不正确或合约未正确部署");
        } else if (error.message.includes("network")) {
            console.log("💡 可能原因: PXA链连接问题，请检查链是否正在运行");
        }
        
        process.exit(1);
    }
}

// 错误处理
if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("❌ 脚本执行失败:", error);
            process.exit(1);
        });
}

module.exports = main;
