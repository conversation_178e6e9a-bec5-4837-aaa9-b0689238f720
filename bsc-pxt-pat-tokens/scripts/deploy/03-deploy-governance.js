const { ethers, network } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("=== 开始部署治理系统 ===");
    console.log("网络:", network.name);
    console.log("链ID:", network.config.chainId);
    
    const [deployer, treasury, operator] = await ethers.getSigners();
    console.log("部署账户:", deployer.address);
    
    // 治理系统配置参数
    const VOTING_DELAY = 24 * 60 * 60; // 1天的投票延迟
    const VOTING_PERIOD = 14 * 24 * 60 * 60; // 14天的投票期
    const PROPOSAL_THRESHOLD = ethers.parseEther("10000"); // 1万PXT提案门槛
    const QUORUM_THRESHOLD = 1000; // 10% 最低投票率(基点)
    const TIMELOCK_DELAY = 48 * 60 * 60; // 48小时时间锁
    
    // 获取已部署的合约地址
    let pxtokenAddress, patokenAddress, stakingPoolAddress;
    
    try {
        const coreFile = path.join(__dirname, "../../deployments", network.name, "core-deployment.json");
        const stakingFile = path.join(__dirname, "../../deployments", network.name, "staking-deployment.json");
        
        const coreDeployment = JSON.parse(fs.readFileSync(coreFile, 'utf8'));
        const stakingDeployment = JSON.parse(fs.readFileSync(stakingFile, 'utf8'));

        console.log("调试 - 质押部署数据:", JSON.stringify(stakingDeployment, null, 2));

        pxtokenAddress = coreDeployment.contracts.PXToken.address;
        patokenAddress = coreDeployment.contracts.PAToken.address;
        stakingPoolAddress = stakingDeployment.contracts.StakingPool;

        console.log("调试 - 读取的质押池地址:", stakingPoolAddress);
        console.log("✅ 已加载依赖合约地址");
    } catch (error) {
        console.error("❌ 未找到依赖的部署文件，请先运行:");
        console.error("1. npx hardhat run scripts/deploy/01-deploy-core-tokens.js --network", network.name);
        console.error("2. npx hardhat run scripts/deploy/02-deploy-staking-system.js --network", network.name);
        process.exit(1);
    }
    
    console.log("PXT代币地址:", pxtokenAddress);
    console.log("PAT代币地址:", patokenAddress);
    console.log("质押池地址:", stakingPoolAddress);
    
    console.log("\n=== 1. 部署投票合约 ===");
    const Voting = await ethers.getContractFactory("Voting");
    const voting = await Voting.deploy();
    await voting.waitForDeployment();
    console.log("✅ 投票合约部署成功:", await voting.getAddress());

    // 初始化投票合约
    await voting.initialize(pxtokenAddress, stakingPoolAddress);
    console.log("✅ 投票合约初始化完成");

    console.log("\n=== 2. 部署提案管理器 ===");
    const ProposalManager = await ethers.getContractFactory("ProposalManager");
    const proposalManager = await ProposalManager.deploy();
    await proposalManager.waitForDeployment();
    console.log("✅ 提案管理器部署成功:", await proposalManager.getAddress());

    // 初始化提案管理器
    await proposalManager.initialize(pxtokenAddress, stakingPoolAddress, voting.address);
    console.log("✅ 提案管理器初始化完成");

    console.log("\n=== 3. 部署DAO主合约 ===");
    const DAO = await ethers.getContractFactory("DAO");
    const dao = await DAO.deploy();
    await dao.waitForDeployment();
    console.log("✅ DAO主合约部署成功:", await dao.getAddress());

    // 初始化DAO合约
    await dao.initialize(
        pxtokenAddress,
        patokenAddress,
        stakingPoolAddress,
        proposalManager.address
    );
    console.log("✅ DAO主合约初始化完成");

    console.log("\n=== 4. 部署国库 ===");
    const Treasury = await ethers.getContractFactory("Treasury");
    const treasuryContract = await Treasury.deploy(
        await dao.getAddress() // DAO地址
    );
    await treasuryContract.waitForDeployment();
    console.log("✅ 国库合约部署成功:", await treasuryContract.getAddress());
    
    console.log("\n=== 5. 配置治理系统关系 ===");

    // 更新Treasury的DAO地址
    await treasuryContract.updateDAOContract(await dao.getAddress());
    console.log("✅ Treasury DAO地址更新完成");

    // 设置提案管理器的DAO地址
    await proposalManager.updateDAOAddress(await dao.getAddress());
    console.log("✅ 提案管理器配置完成");

    // 注意：Voting合约不需要设置ProposalManager地址，通过角色权限管理
    
    console.log("\n=== 6. 验证治理系统 ===");

    try {
        // 验证合约是否正确初始化
        const pxtokenAddr = await voting.pxtoken();
        const stakingPoolAddr = await voting.stakingPool();
        console.log("投票合约PXT代币地址:", pxtokenAddr);
        console.log("投票合约质押池地址:", stakingPoolAddr);

        // 验证基本参数
        const minParticipationRate = await voting.minParticipationRate();
        console.log("最低参与率:", minParticipationRate.toString(), "%");

    } catch (error) {
        console.log("验证过程中出现问题:", error.message);
    }
    
    // 保存部署信息
    const deploymentInfo = {
        network: network.name,
        chainId: network.config.chainId,
        timestamp: new Date().toISOString(),
        deployer: deployer.address,
        contracts: {
            Treasury: {
                address: await treasuryContract.getAddress(),
                description: "国库合约"
            },
            Voting: {
                address: await voting.getAddress(),
                description: "投票系统"
            },
            ProposalManager: {
                address: await proposalManager.getAddress(),
                description: "提案管理器"
            },
            DAO: {
                address: await dao.getAddress(),
                description: "DAO主合约"
            }
        },
        configuration: {
            proposalThreshold: ethers.formatEther(PROPOSAL_THRESHOLD) + " PXT",
            quorumThreshold: (QUORUM_THRESHOLD / 100) + "%",
            votingDelay: (VOTING_DELAY / (24 * 60 * 60)) + " 天",
            votingPeriod: (VOTING_PERIOD / (24 * 60 * 60)) + " 天",
            timelockDelay: (TIMELOCK_DELAY / (60 * 60)) + " 小时"
        },
        dependencies: {
            PXToken: pxtokenAddress,
            PAToken: patokenAddress,
            StakingPool: stakingPoolAddress
        }
    };
    
    // 保存部署文件
    const deploymentDir = path.join(__dirname, "../../deployments", network.name);
    const deploymentFile = path.join(deploymentDir, "governance-deployment.json");
    fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
    
    console.log("\n=== 部署完成 ===");
    console.log("部署信息已保存到:", deploymentFile);
    console.log("\n下一步:");
    console.log("1. 测试治理功能: npx hardhat run scripts/test/03-governance-test.js --network", network.name);
    console.log("2. 创建测试提案: npx hardhat run scripts/admin/create-test-proposal.js --network", network.name);
    
    return {
        treasury: await treasuryContract.getAddress(),
        voting: await voting.getAddress(),
        proposalManager: await proposalManager.getAddress(),
        dao: await dao.getAddress()
    };
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("治理系统部署失败:", error);
            process.exit(1);
        });
}

module.exports = main;
