🔍 检查清理结果...
⚠️  仍有        1 个相关文件存在
剩余文件:
./bsc-chain-setup.sh
📋 当前目录内容:
total 115912
drwxr-xr-x@  9 <USER>  <GROUP>       288  7 28 21:22 .
drwxrwxr-x@ 21 <USER>  <GROUP>       672  8  3 09:19 ..
-rwxr-xr-x@  1 <USER>  <GROUP>     10979  7 20 21:49 bsc-chain-setup.sh
-rwxr-xr-x@  1 <USER>  <GROUP>      2627  7 18 22:39 clean-bsc-chain.sh
-rwxr-xr-x@  1 <USER>  <GROUP>  59304496  7 19 10:49 polygon-edge
-rwxr-xr-x@  1 <USER>  <GROUP>      1712  7 10 10:52 restart-local-bsc.sh
-rwxr-xr-x@  1 <USER>  <GROUP>      6522  7  9 17:28 setup-local-bsc.sh
-rwxr-xr-x@  1 <USER>  <GROUP>      6710  7 20 14:30 start-background.sh
-rwxr-xr-x@  1 <USER>  <GROUP>      1611  7 20 14:33 stop-bsc-chain.sh

🎉 BSC链清理完成！
💡 现在可以运行以下命令重新启动BSC链:
   ./setup-local-bsc.sh

junziliuyi@jzy local-bsc-chain % clear
junziliuyi@jzy local-bsc-chain % ./bsc-chain-setup.sh
🚀 启动 BSC 本地链...
================================================
🧹 清理旧数据...
🔑 生成验证者节点密钥...


[WARNING: INSECURE LOCAL SECRETS - SHOULD NOT BE RUN IN PRODUCTION]

[SECRETS INIT]
Public key (address) = ******************************************
BLS Public key       = 0x897583771039e776893dd83204bdc0e78458adbfe71d4890cc34520cf4add7ee30477199af5747c8b4711e7e8b9098f6
Node ID              = 16Uiu2HAm73tU4yvspewMf7cFZQoEvs8J8KVRBFKvNPffNfX6tXm3



[WARNING: INSECURE LOCAL SECRETS - SHOULD NOT BE RUN IN PRODUCTION]

[SECRETS INIT]
Public key (address) = ******************************************
BLS Public key       = 0xa4fb72d70bf162c80ab93335afc93c3aca091cbfc1b053537a6addce9d9b6a3bf129d5bc5be65636d4bc559bba166405
Node ID              = 16Uiu2HAmU5zdLbefJbz3F7RJv8yCzqX136eLXCfgucw2BX9rcYHm



[WARNING: INSECURE LOCAL SECRETS - SHOULD NOT BE RUN IN PRODUCTION]

[SECRETS INIT]
Public key (address) = ******************************************
BLS Public key       = 0xb6691089cec22d7acbac93c4373c9e7c2ac3094775cf493bcd6424c24c01cf8da974c2dfa4a719a42cda321417b7f7c8
Node ID              = 16Uiu2HAmQxasM87qvkL6gHny4uY1saNPmH6CKxmkFzsqBrNzZxwA



[WARNING: INSECURE LOCAL SECRETS - SHOULD NOT BE RUN IN PRODUCTION]

[SECRETS INIT]
Public key (address) = 0x0A0fC56Bf3C15F9c73D0fA4F742e83aC3dE719F4
BLS Public key       = 0xb293cf21f44b4ca41128939b873797cb7533f6928a652adf20c66ec7c378e9776aedc4fdf5266eeb1aeac5f5c5d5074e
Node ID              = 16Uiu2HAm25bkmCrXh7JF7cCHJTAWKnoiRjnFLGevJ5tAGd31BndD

🔑 获取节点信息...
✅ 节点ID: 16Uiu2HAm73tU4yvspewMf7cFZQoEvs8J8KVRBFKvNPffNfX6tXm3
🔑 生成多个账户...
🔑 生成额外测试用户账户...
✅ 账户生成完成:
   部署者: ******************************************
   国库: ******************************************
   操作员: ******************************************
   测试用户1: 0x0A0fC56Bf3C15F9c73D0fA4F742e83aC3dE719F4
🔑 私钥已生成 (用于MetaMask导入)
   [警告] 这些是开发密钥，请勿在主网使用!
🔧 更新.env文件中的本地链配置...
💾 已备份原有.env文件为.env.backup
✅ 已更新.env文件并保存所有账户信息
💡 提示: 原有配置已保留，仅更新了私钥部分
📜 创建创世文件...


[GENESIS SUCCESS]

Genesis written to ./genesis.json

✅ 创世文件创建完成，已为所有账户注入预分配余额
✅ 创世文件创建完成，已为所有账户注入预分配余额
💰 余额分配:
   部署者: 10,000 BNB
   国库: 5,000 BNB
   操作员: 3,000 BNB
   测试用户1: 2,000 BNB
🔍 调试: 正在检查最终的创世文件内容...
预分配信息:
{
  "0x0A0fC56Bf3C15F9c73D0fA4F742e83aC3dE719F4": {
    "balance": "0x6c6b935b8bbd400000"
  },
  "******************************************": {
    "balance": "0xa2a15d09519be00000"
  },
  "******************************************": {
    "balance": "0x21e19e0c9bab2400000"
  },
  "******************************************": {
    "balance": "0x10f0cf064dd59200000"
  }
}
🔍 调试结束。
🔥 启动所有验证者节点...
▶️ 启动节点1...

2025-08-03T17:08:22.196+0800 [INFO]  polygon.server: Data dir: path=./bsc-chain-1
2025-08-03T17:08:22.259+0800 [INFO]  polygon.blockchain: genesis: hash=0x8aafa17c7336211c5efc3212f2e168d94e6ddf48e754b510fbf3a67254abb001
2025-08-03T17:08:22.259+0800 [INFO]  polygon.server.ibft: validator key: addr=******************************************
2025-08-03T17:08:22.260+0800 [INFO]  polygon.server: GRPC server running: addr=127.0.0.1:19632
2025-08-03T17:08:22.260+0800 [INFO]  polygon.network: LibP2P server running: addr=/ip4/127.0.0.1/tcp/20001/p2p/16Uiu2HAm73tU4yvspewMf7cFZQoEvs8J8KVRBFKvNPffNfX6tXm3
2025-08-03T17:08:22.260+0800 [INFO]  polygon.network: Omitting bootnode with same ID as host: id=16Uiu2HAm73tU4yvspewMf7cFZQoEvs8J8KVRBFKvNPffNfX6tXm3
2025-08-03T17:08:22.260+0800 [INFO]  polygon.txpool.event-manager: Added new subscription 3263582320
2025-08-03T17:08:22.260+0800 [INFO]  polygon.server.jsonrpc: http server started: addr=127.0.0.1:18485
2025-08-03T17:08:22.261+0800 [INFO]  polygon.server.ibft.consensus: sequence started: height=1
2025-08-03T17:08:22.261+0800 [INFO]  polygon.server.ibft.consensus: round started: round=0
2025-08-03T17:08:22.262+0800 [INFO]  polygon.server.ibft.consensus: we are the proposer
2025-08-03T17:08:24.001+0800 [INFO]  polygon.server.ibft: executed txs: successful=0 failed=0 skipped=0 remaining=0
2025-08-03T17:08:24.005+0800 [INFO]  polygon.server.ibft: build block: number=1 txs=0
▶️ 启动节点2...

2025-08-03T17:08:24.224+0800 [INFO]  polygon.server: Data dir: path=./bsc-chain-2
2025-08-03T17:08:24.311+0800 [INFO]  polygon.blockchain: genesis: hash=0x8aafa17c7336211c5efc3212f2e168d94e6ddf48e754b510fbf3a67254abb001
2025-08-03T17:08:24.311+0800 [INFO]  polygon.server.ibft: validator key: addr=******************************************
2025-08-03T17:08:24.311+0800 [INFO]  polygon.server: GRPC server running: addr=127.0.0.1:19633
2025-08-03T17:08:24.311+0800 [INFO]  polygon.network: LibP2P server running: addr=/ip4/127.0.0.1/tcp/20002/p2p/16Uiu2HAmU5zdLbefJbz3F7RJv8yCzqX136eLXCfgucw2BX9rcYHm
2025-08-03T17:08:24.311+0800 [INFO]  polygon.server.jsonrpc: http server started: addr=127.0.0.1:18485
listen tcp 127.0.0.1:18485: bind: address already in use
▶️ 启动节点3...

2025-08-03T17:08:25.232+0800 [INFO]  polygon.server: Data dir: path=./bsc-chain-3
2025-08-03T17:08:25.330+0800 [INFO]  polygon.blockchain: genesis: hash=0x8aafa17c7336211c5efc3212f2e168d94e6ddf48e754b510fbf3a67254abb001
2025-08-03T17:08:25.330+0800 [INFO]  polygon.server.ibft: validator key: addr=******************************************
2025-08-03T17:08:25.330+0800 [INFO]  polygon.server: GRPC server running: addr=127.0.0.1:19634
2025-08-03T17:08:25.330+0800 [INFO]  polygon.network: LibP2P server running: addr=/ip4/127.0.0.1/tcp/20003/p2p/16Uiu2HAmQxasM87qvkL6gHny4uY1saNPmH6CKxmkFzsqBrNzZxwA
2025-08-03T17:08:25.330+0800 [INFO]  polygon.server.jsonrpc: http server started: addr=127.0.0.1:18486
2025-08-03T17:08:25.331+0800 [INFO]  polygon.txpool.event-manager: Added new subscription 4097420835
2025-08-03T17:08:25.332+0800 [INFO]  polygon.server.ibft.consensus: sequence started: height=1
2025-08-03T17:08:25.333+0800 [INFO]  polygon.server.ibft.consensus: round started: round=0
2025-08-03T17:08:25.355+0800 [INFO]  polygon.network: Peer connected: id=16Uiu2HAmQxasM87qvkL6gHny4uY1saNPmH6CKxmkFzsqBrNzZxwA
2025-08-03T17:08:25.355+0800 [INFO]  polygon.network: Peer connected: id=16Uiu2HAm73tU4yvspewMf7cFZQoEvs8J8KVRBFKvNPffNfX6tXm3
▶️ 启动节点4...

2025-08-03T17:08:26.251+0800 [INFO]  polygon.server: Data dir: path=./bsc-chain-4
2025-08-03T17:08:26.307+0800 [INFO]  polygon.blockchain: genesis: hash=0x8aafa17c7336211c5efc3212f2e168d94e6ddf48e754b510fbf3a67254abb001
2025-08-03T17:08:26.307+0800 [INFO]  polygon.server.ibft: validator key: addr=0x0A0fC56Bf3C15F9c73D0fA4F742e83aC3dE719F4
2025-08-03T17:08:26.307+0800 [INFO]  polygon.server: GRPC server running: addr=127.0.0.1:19635
2025-08-03T17:08:26.307+0800 [INFO]  polygon.network: LibP2P server running: addr=/ip4/127.0.0.1/tcp/20004/p2p/16Uiu2HAm25bkmCrXh7JF7cCHJTAWKnoiRjnFLGevJ5tAGd31BndD
2025-08-03T17:08:26.308+0800 [INFO]  polygon.server.jsonrpc: http server started: addr=127.0.0.1:18487
2025-08-03T17:08:26.308+0800 [INFO]  polygon.txpool.event-manager: Added new subscription 3597281180
2025-08-03T17:08:26.310+0800 [INFO]  polygon.server.ibft.consensus: sequence started: height=1
2025-08-03T17:08:26.310+0800 [INFO]  polygon.server.ibft.consensus: round started: round=0
2025-08-03T17:08:26.326+0800 [INFO]  polygon.network: Peer connected: id=16Uiu2HAm25bkmCrXh7JF7cCHJTAWKnoiRjnFLGevJ5tAGd31BndD
2025-08-03T17:08:26.327+0800 [INFO]  polygon.network: Peer connected: id=16Uiu2HAm73tU4yvspewMf7cFZQoEvs8J8KVRBFKvNPffNfX6tXm3
✅ 所有节点已启动
🌐 主节点 RPC: http://127.0.0.1:18485
💰 预分配账户:
   部署者: ****************************************** (10,000 BNB)
   国库: ****************************************** (5,000 BNB)
   操作员: ****************************************** (3,000 BNB)
   测试用户1: 0x0A0fC56Bf3C15F9c73D0fA4F742e83aC3dE719F4 (2,000 BNB)
⏳ 等待链启动...
2025-08-03T17:08:30.353+0800 [INFO]  polygon.network: Peer connected: id=16Uiu2HAm25bkmCrXh7JF7cCHJTAWKnoiRjnFLGevJ5tAGd31BndD
2025-08-03T17:08:30.353+0800 [INFO]  polygon.network: Peer connected: id=16Uiu2HAmQxasM87qvkL6gHny4uY1saNPmH6CKxmkFzsqBrNzZxwA
2025-08-03T17:08:35.265+0800 [INFO]  polygon.server.ibft.consensus: round timeout expired: round=0
2025-08-03T17:08:35.268+0800 [INFO]  polygon.server.ibft.consensus: round started: round=1
🧪 测试链状态...
✅ 链状态正常，当前区块高度: 0x0
💰 检查主要账户余额...
🔍 调试: 部署者余额查询响应: {"jsonrpc":"2.0","id":1,"result":"0x21e19e0c9bab2400000"}
💰 部署者账户余额: 10,000.0000 BNB
💰 国库账户余额: 5,000.0000 BNB

====================================
🎉 BSC本地链启动成功！
====================================
MetaMask网络配置:
网络名称: BSC Local Test
RPC URL: http://127.0.0.1:18485
链ID: 97
货币符号: BNB
====================================
账户信息 (已保存到.env文件):
部署者: ******************************************
国库: ******************************************
操作员: ******************************************
测试用户1: 0x0A0fC56Bf3C15F9c73D0fA4F742e83aC3dE719F4
====================================
🛑 停止链: kill `cat bsc-chain.pid` 或按Ctrl+C
====================================
按Enter键停止链，或按Ctrl+C后台运行...

junziliuyi@jzy bsc-pxt-pat-tokens % npx hardhat run scripts/deploy/01-deploy-core-tokens.js --network localhost
=== 开始部署核心代币系统 ===
网络: localhost
链ID: 97
部署账户: ******************************************
国库账户: ******************************************
操作员账户: ******************************************
部署账户余额: 10000.0 ETH

=== 创建池子账户 ===
中国大陆池子地址: ******************************************
国际池子地址: ******************************************
质押池地址: ******************************************
跨链池地址: ******************************************

=== 1. 部署 PXT 治理代币 ===
✅ PXT代币部署成功: ******************************************

=== 2. 部署 PAT 功能代币 ===
✅ PAT代币部署成功: ******************************************

=== 3. 部署代币注册表 ===
✅ 代币注册表部署成功: ******************************************

=== 4. 配置代币注册表 ===
注册表合约owner: ******************************************
部署者地址: ******************************************
是否为owner: true
正在添加工厂权限...
✅ 部署者已添加为工厂
验证工厂权限: true
准备注册PXT代币:
- 地址: ******************************************
- 名称: Platform Governance Token
- 符号: PXT
- 小数位: 18n
- 总供应量: 100000000.0
✅ PXT代币已注册到注册表
准备注册PAT代币:
- 地址: ******************************************
- 名称: PX Activity Token
- 符号: PAT
- 小数位: 18n
- 总供应量: 300000000.0
✅ PAT代币已注册到注册表

=== 5. 验证部署结果 ===
PXT总供应量: 100000000.0
PAT总供应量: 300000000.0
部署者PXT余额: 55002000.0
部署者PAT余额: 0.0

=== 部署完成 ===
部署信息已保存到: /Users/<USER>/Desktop/PXA/bsc-pxt-pat-tokens/deployments/localhost/core-deployment.json

下一步:
1. 部署质押系统: npx hardhat run scripts/deploy/02-deploy-staking-system.js --network localhost
2. 部署治理系统: npx hardhat run scripts/deploy/03-deploy-governance.js --network localhost
junziliuyi@jzy bsc-pxt-pat-tokens % npx hardhat run scripts/deploy/02-deploy-staking-system.js --network localhost
=== 开始部署质押系统 ===
网络: localhost
链ID: 97
部署账户: ******************************************
✅ 已加载核心合约地址
- PXT代币: ******************************************
- PAT代币: ******************************************
- 代币注册表: ******************************************
PXT代币地址: ******************************************
PAT代币地址: ******************************************

=== 1. 部署质押工厂 ===
✅ 质押工厂部署成功: 0x8FEA3b95A9e1e851cA08B569a9bc53D011258479

=== 2. 部署质押池 ===
✅ 质押池部署成功: 0xbbE57AD5CeD6434f8c73C871752685fB09279746

=== 3. 初始化质押池 ===
✅ 质押池初始化完成

=== 4. 部署奖励分配器 ===
✅ 奖励分配器部署成功: 0x2ef901a55EEf9cbfF6a2CA906F57c776093A0EA6

=== 5. 配置质押系统关系 ===
✅ 质押池已添加到奖励分配器
✅ 已设置奖励分配器
✅ 已设置质押池实现合约

=== 6. 质押等级已预配置 ===
质押等级在合约构造函数中已预设：
- 丁级: 100 PXT
- 丙级: 1,000 PXT
- 乙级: 5,000 PXT
- 甲级: 20,000 PXT
- 十绝: 100,000 PXT
- 双十绝: 250,000 PXT
- 至尊: 500,000 PXT

=== 7. 验证质押系统 ===
最小质押金额: 1.0 PXT
基础年化收益率: 500 基点

=== 质押系统部署完成 ===
部署信息已保存到: /Users/<USER>/Desktop/PXA/bsc-pxt-pat-tokens/deployments/localhost/staking-deployment.json

下一步:
1. 部署治理系统: npx hardhat run scripts/deploy/03-deploy-governance.js --network localhost
2. 测试质押功能: npx hardhat run scripts/test/02-reward-system-test.js --network localhost

junziliuyi@jzy bsc-pxt-pat-tokens % clear
junziliuyi@jzy bsc-pxt-pat-tokens % npx hardhat run scripts/deploy/03-deploy-governance.js --network localhost
=== 开始部署治理系统 ===
网络: localhost
链ID: 97
部署账户: ******************************************
调试 - 质押部署数据: {
  "network": "localhost",
  "chainId": 97,
  "timestamp": "2025-08-03T09:15:42.619Z",
  "deployer": "******************************************",
  "treasury": "******************************************",
  "contracts": {
    "StakingFactory": "0x8FEA3b95A9e1e851cA08B569a9bc53D011258479",
    "StakingPool": "0xbbE57AD5CeD6434f8c73C871752685fB09279746",
    "RewardDistributor": "0x2ef901a55EEf9cbfF6a2CA906F57c776093A0EA6"
  },
  "configuration": {
    "minStakeAmount": "1000000000000000000",
    "baseAPY": "500",
    "stakeLevels": 7
  }
}
调试 - 读取的质押池地址: 0xbbE57AD5CeD6434f8c73C871752685fB09279746
✅ 已加载依赖合约地址
PXT代币地址: ******************************************
PAT代币地址: ******************************************
质押池地址: 0xbbE57AD5CeD6434f8c73C871752685fB09279746

=== 1. 部署投票合约 ===
✅ 投票合约部署成功: 0xa6Bf3F21504941640FE63F6D31B978a58931d818
✅ 投票合约初始化完成

=== 2. 部署提案管理器 ===
✅ 提案管理器部署成功: 0x5909257Ecb693b493B1B87eCA0D0FfFA67E53760
✅ 提案管理器初始化完成

=== 3. 部署DAO主合约 ===
✅ DAO主合约部署成功: 0xDbdE05F64636f65AF51c3D112d7ACf158fFD5c92
✅ DAO主合约初始化完成

=== 4. 部署国库 ===
✅ 国库合约部署成功: ******************************************

=== 5. 配置治理系统关系 ===
✅ Treasury DAO地址更新完成
✅ 提案管理器配置完成

=== 6. 验证治理系统 ===
投票合约PXT代币地址: ******************************************
投票合约质押池地址: 0xbbE57AD5CeD6434f8c73C871752685fB09279746
最低参与率: 10 %

=== 部署完成 ===
部署信息已保存到: /Users/<USER>/Desktop/PXA/bsc-pxt-pat-tokens/deployments/localhost/governance-deployment.json

下一步:
1. 测试治理功能: npx hardhat run scripts/test/03-governance-test.js --network localhost
2. 创建测试提案: npx hardhat run scripts/admin/create-test-proposal.js --network localhost
junziliuyi@jzy bsc-pxt-pat-tokens % 


junziliuyi@jzy bsc-pxt-pat-tokens % npx hardhat run scripts/deploy/04-deploy-content-system.js --network localhost
=== 开始部署内容上链系统 ===
网络: localhost
链ID: 97
部署账户: ******************************************
部署者余额: 9999.96 ETH
✅ 已加载依赖合约地址
- PAT代币: ******************************************
- 国库合约: ******************************************

=== 1. 部署ContentRegistry合约 ===
正在部署ContentRegistry...
✅ ContentRegistry部署成功: ******************************************
验证ContentRegistry配置...
  PAT代币地址: ******************************************
  国库地址: ******************************************

=== 2. 部署ContentCharacter合约 ===
正在部署ContentCharacter...
✅ ContentCharacter部署成功: ******************************************

=== 3. 部署ContentMint合约 ===
正在部署ContentMint...
✅ ContentMint部署成功: ******************************************
验证ContentMint配置...
  ContentRegistry地址: ******************************************
  国库地址: ******************************************

=== 4. 测试基础功能 ===
测试内容类型费用...
  视频费用: 1.0 PAT
  文章费用: 0.05 PAT
测试统计信息...
  总内容数: 0
  活跃内容数: 0
  总PAT消耗: 0.0 PAT

=== 5. 生成验证命令 ===
ContentRegistry验证命令:
  npx hardhat verify --network bscTestnet ****************************************** "******************************************" "******************************************"
ContentCharacter验证命令:
  npx hardhat verify --network bscTestnet ******************************************
ContentMint验证命令:
  npx hardhat verify --network bscTestnet ****************************************** "******************************************" "******************************************"

=== 6. 保存部署信息 ===
✅ 部署信息已保存到: /Users/<USER>/Desktop/PXA/bsc-pxt-pat-tokens/deployments/localhost/content-deployment.json

=== 内容上链系统部署完成 ===
ContentRegistry: ******************************************
ContentCharacter: ******************************************
ContentMint: ******************************************

下一步:
1. 在区块链浏览器上验证合约
2. 集成IPFS存储功能
3. 运行完整功能测试
4. 配置前端应用集成

